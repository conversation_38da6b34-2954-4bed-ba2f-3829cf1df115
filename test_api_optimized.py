#!/usr/bin/env python3
"""
测试优化后的API模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.api_optimized import (
    get_financial_metrics,
    get_financial_statements,
    get_market_data,
    get_price_history,
    FinancialDataFetcher
)

def test_financial_data_fetcher():
    """测试FinancialDataFetcher类"""
    print("=" * 50)
    print("测试 FinancialDataFetcher 类")
    print("=" * 50)
    
    fetcher = FinancialDataFetcher()
    symbol = "000001"  # 平安银行
    
    # 测试获取实时数据
    print("\n1. 测试获取实时数据...")
    realtime_data = fetcher._get_realtime_data(symbol)
    if realtime_data is not None:
        print(f"✓ 成功获取实时数据，股票名称: {realtime_data.get('名称', 'N/A')}")
        print(f"  当前价格: {realtime_data.get('最新价', 'N/A')}")
        print(f"  总市值: {realtime_data.get('总市值', 'N/A')}")
    else:
        print("✗ 获取实时数据失败")
    
    # 测试获取财务指标
    print("\n2. 测试获取财务指标...")
    financial_indicators = fetcher._get_financial_indicators(symbol)
    if financial_indicators is not None:
        print(f"✓ 成功获取财务指标数据")
        print(f"  数据日期: {financial_indicators.get('日期', 'N/A')}")
        print(f"  净资产收益率: {financial_indicators.get('净资产收益率(%)', 'N/A')}%")
    else:
        print("✗ 获取财务指标失败")
    
    # 测试获取财务报表
    print("\n3. 测试获取财务报表...")
    latest_income, previous_income = fetcher._get_financial_report(symbol, "利润表")
    if latest_income is not None:
        print(f"✓ 成功获取利润表数据")
        print(f"  营业总收入: {latest_income.get('营业总收入', 'N/A')}")
        print(f"  净利润: {latest_income.get('净利润', 'N/A')}")
    else:
        print("✗ 获取利润表失败")

def test_financial_metrics():
    """测试get_financial_metrics函数"""
    print("\n" + "=" * 50)
    print("测试 get_financial_metrics 函数")
    print("=" * 50)
    
    symbol = "000001"  # 平安银行
    
    print(f"\n获取 {symbol} 的财务指标...")
    metrics = get_financial_metrics(symbol)
    
    if metrics and len(metrics) > 0 and metrics[0]:
        print("✓ 成功获取财务指标")
        metric_data = metrics[0]
        
        print("\n主要财务指标:")
        print(f"  净资产收益率: {metric_data.get('return_on_equity', 'N/A'):.4f}")
        print(f"  销售净利率: {metric_data.get('net_margin', 'N/A'):.4f}")
        print(f"  营业利润率: {metric_data.get('operating_margin', 'N/A'):.4f}")
        print(f"  市盈率: {metric_data.get('pe_ratio', 'N/A')}")
        print(f"  市净率: {metric_data.get('price_to_book', 'N/A')}")
        print(f"  市销率: {metric_data.get('price_to_sales', 'N/A'):.4f}")
        
        print(f"\n增长指标:")
        print(f"  营收增长率: {metric_data.get('revenue_growth', 'N/A'):.4f}")
        print(f"  净利润增长率: {metric_data.get('earnings_growth', 'N/A'):.4f}")
        print(f"  净资产增长率: {metric_data.get('book_value_growth', 'N/A'):.4f}")
        
        print(f"\n财务健康指标:")
        print(f"  流动比率: {metric_data.get('current_ratio', 'N/A')}")
        print(f"  资产负债率: {metric_data.get('debt_to_equity', 'N/A'):.4f}")
        print(f"  每股收益: {metric_data.get('earnings_per_share', 'N/A')}")
        print(f"  每股经营现金流: {metric_data.get('free_cash_flow_per_share', 'N/A')}")
    else:
        print("✗ 获取财务指标失败")

def test_financial_statements():
    """测试get_financial_statements函数"""
    print("\n" + "=" * 50)
    print("测试 get_financial_statements 函数")
    print("=" * 50)
    
    symbol = "000001"  # 平安银行
    
    print(f"\n获取 {symbol} 的财务报表...")
    statements = get_financial_statements(symbol)
    
    if statements and len(statements) >= 2:
        print("✓ 成功获取财务报表")
        
        current_data = statements[0]
        previous_data = statements[1]
        
        print("\n最新期财务数据:")
        print(f"  营业收入: {current_data.get('operating_revenue', 'N/A'):,.0f}")
        print(f"  净利润: {current_data.get('net_income', 'N/A'):,.0f}")
        print(f"  营业利润: {current_data.get('operating_profit', 'N/A'):,.0f}")
        print(f"  营运资金: {current_data.get('working_capital', 'N/A'):,.0f}")
        print(f"  自由现金流: {current_data.get('free_cash_flow', 'N/A'):,.0f}")
        print(f"  资本支出: {current_data.get('capital_expenditure', 'N/A'):,.0f}")
        
        print("\n上期财务数据:")
        print(f"  营业收入: {previous_data.get('operating_revenue', 'N/A'):,.0f}")
        print(f"  净利润: {previous_data.get('net_income', 'N/A'):,.0f}")
        print(f"  营业利润: {previous_data.get('operating_profit', 'N/A'):,.0f}")
        print(f"  营运资金: {previous_data.get('working_capital', 'N/A'):,.0f}")
        print(f"  自由现金流: {previous_data.get('free_cash_flow', 'N/A'):,.0f}")
        print(f"  资本支出: {previous_data.get('capital_expenditure', 'N/A'):,.0f}")
    else:
        print("✗ 获取财务报表失败")

def test_market_data():
    """测试get_market_data函数"""
    print("\n" + "=" * 50)
    print("测试 get_market_data 函数")
    print("=" * 50)
    
    symbol = "000001"  # 平安银行
    
    print(f"\n获取 {symbol} 的市场数据...")
    market_data = get_market_data(symbol)
    
    if market_data:
        print("✓ 成功获取市场数据")
        print(f"  总市值: {market_data.get('market_cap', 'N/A'):,.0f}")
        print(f"  成交量: {market_data.get('volume', 'N/A'):,.0f}")
        print(f"  平均成交量: {market_data.get('average_volume', 'N/A'):,.0f}")
        print(f"  52周最高: {market_data.get('fifty_two_week_high', 'N/A')}")
        print(f"  52周最低: {market_data.get('fifty_two_week_low', 'N/A')}")
    else:
        print("✗ 获取市场数据失败")

def main():
    """主测试函数"""
    print("开始测试优化后的API模块...")
    
    try:
        # 测试FinancialDataFetcher类
        test_financial_data_fetcher()
        
        # 测试各个函数
        test_financial_metrics()
        test_financial_statements()
        test_market_data()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

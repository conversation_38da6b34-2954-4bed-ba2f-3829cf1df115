from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import json
import numpy as np
from src.utils.logging_config import setup_logger

# 设置日志记录
logger = setup_logger('api_optimized')


class FinancialDataFetcher:
    """财务数据获取器，提供统一的数据获取和处理接口"""
    
    def __init__(self):
        self.cache = {}  # 简单的缓存机制
    
    def _get_realtime_data(self, symbol: str) -> Optional[pd.Series]:
        """获取实时行情数据"""
        try:
            logger.info("Fetching real-time quotes...")
            realtime_data = ak.stock_zh_a_spot_em()
            if realtime_data is None or realtime_data.empty:
                logger.warning("No real-time quotes data available")
                return None

            stock_data = realtime_data[realtime_data['代码'] == symbol]
            if stock_data.empty:
                logger.warning(f"No real-time quotes found for {symbol}")
                return None

            logger.info("✓ Real-time quotes fetched")
            return stock_data.iloc[0]
        except Exception as e:
            logger.error(f"Error fetching real-time data: {e}")
            return None
    
    def _get_financial_indicators(self, symbol: str) -> Optional[pd.Series]:
        """获取财务指标数据"""
        try:
            logger.info("Fetching Sina financial indicators...")
            current_year = datetime.now().year
            financial_data = ak.stock_financial_analysis_indicator(
                symbol=symbol, start_year=str(current_year-1))
            
            if financial_data is None or financial_data.empty:
                logger.warning("No financial indicator data available")
                return None

            # 按日期排序并获取最新的数据
            financial_data['日期'] = pd.to_datetime(financial_data['日期'])
            financial_data = financial_data.sort_values('日期', ascending=False)
            latest_financial = financial_data.iloc[0] if not financial_data.empty else pd.Series()
            
            logger.info(f"✓ Financial indicators fetched ({len(financial_data)} records)")
            logger.info(f"Latest data date: {latest_financial.get('日期')}")
            return latest_financial
        except Exception as e:
            logger.error(f"Error fetching financial indicators: {e}")
            return None
    
    def _get_financial_report(self, symbol: str, report_type: str) -> Tuple[Optional[pd.Series], Optional[pd.Series]]:
        """获取财务报表数据
        
        Args:
            symbol: 股票代码
            report_type: 报表类型 ('利润表', '资产负债表', '现金流量表')
            
        Returns:
            Tuple[最新期数据, 上一期数据]
        """
        try:
            logger.info(f"Fetching {report_type}...")
            report_data = ak.stock_financial_report_sina(
                stock=f"sh{symbol}", symbol=report_type)
            
            if not report_data.empty:
                latest_data = report_data.iloc[0]
                previous_data = report_data.iloc[1] if len(report_data) > 1 else report_data.iloc[0]
                logger.info(f"✓ {report_type} fetched")
                return latest_data, previous_data
            else:
                logger.warning(f"Failed to get {report_type}")
                return None, None
        except Exception as e:
            logger.error(f"Error getting {report_type}: {e}")
            return None, None
    
    @staticmethod
    def _convert_percentage(value: float) -> float:
        """将百分比值转换为小数"""
        try:
            return float(value) / 100.0 if value is not None else 0.0
        except:
            return 0.0
    
    @staticmethod
    def _safe_float(value, default: float = 0.0) -> float:
        """安全转换为浮点数"""
        try:
            return float(value) if value is not None else default
        except:
            return default


def get_financial_metrics(symbol: str) -> Dict[str, Any]:
    """获取财务指标数据"""
    logger.info(f"Getting financial indicators for {symbol}...")
    
    fetcher = FinancialDataFetcher()
    
    try:
        # 获取实时行情数据
        stock_data = fetcher._get_realtime_data(symbol)
        if stock_data is None:
            return [{}]
        
        # 获取财务指标数据
        latest_financial = fetcher._get_financial_indicators(symbol)
        if latest_financial is None:
            return [{}]
        
        # 获取利润表数据
        latest_income, _ = fetcher._get_financial_report(symbol, "利润表")
        if latest_income is None:
            latest_income = pd.Series()
        
        # 构建完整指标数据
        logger.info("Building indicators...")
        try:
            all_metrics = {
                # 市场数据
                "market_cap": fetcher._safe_float(stock_data.get("总市值", 0)),
                "float_market_cap": fetcher._safe_float(stock_data.get("流通市值", 0)),

                # 盈利数据
                "revenue": fetcher._safe_float(latest_income.get("营业总收入", 0)),
                "net_income": fetcher._safe_float(latest_income.get("净利润", 0)),
                "return_on_equity": fetcher._convert_percentage(latest_financial.get("净资产收益率(%)", 0)),
                "net_margin": fetcher._convert_percentage(latest_financial.get("销售净利率(%)", 0)),
                "operating_margin": fetcher._convert_percentage(latest_financial.get("营业利润率(%)", 0)),

                # 增长指标
                "revenue_growth": fetcher._convert_percentage(latest_financial.get("主营业务收入增长率(%)", 0)),
                "earnings_growth": fetcher._convert_percentage(latest_financial.get("净利润增长率(%)", 0)),
                "book_value_growth": fetcher._convert_percentage(latest_financial.get("净资产增长率(%)", 0)),

                # 财务健康指标
                "current_ratio": fetcher._safe_float(latest_financial.get("流动比率", 0)),
                "debt_to_equity": fetcher._convert_percentage(latest_financial.get("资产负债率(%)", 0)),
                "free_cash_flow_per_share": fetcher._safe_float(latest_financial.get("每股经营性现金流(元)", 0)),
                "earnings_per_share": fetcher._safe_float(latest_financial.get("加权每股收益(元)", 0)),

                # 估值比率
                "pe_ratio": fetcher._safe_float(stock_data.get("市盈率-动态", 0)),
                "price_to_book": fetcher._safe_float(stock_data.get("市净率", 0)),
                "price_to_sales": (fetcher._safe_float(stock_data.get("总市值", 0)) / 
                                 fetcher._safe_float(latest_income.get("营业总收入", 1))) 
                                 if fetcher._safe_float(latest_income.get("营业总收入", 0)) > 0 else 0,
            }

            # 只返回 agent 需要的指标
            agent_metrics = {
                # 盈利能力指标
                "return_on_equity": all_metrics["return_on_equity"],
                "net_margin": all_metrics["net_margin"],
                "operating_margin": all_metrics["operating_margin"],

                # 增长指标
                "revenue_growth": all_metrics["revenue_growth"],
                "earnings_growth": all_metrics["earnings_growth"],
                "book_value_growth": all_metrics["book_value_growth"],

                # 财务健康指标
                "current_ratio": all_metrics["current_ratio"],
                "debt_to_equity": all_metrics["debt_to_equity"],
                "free_cash_flow_per_share": all_metrics["free_cash_flow_per_share"],
                "earnings_per_share": all_metrics["earnings_per_share"],

                # 估值比率
                "pe_ratio": all_metrics["pe_ratio"],
                "price_to_book": all_metrics["price_to_book"],
                "price_to_sales": all_metrics["price_to_sales"],
            }

            logger.info("✓ Indicators built successfully")

            # 打印调试信息
            logger.debug("\n获取到的完整指标数据：")
            for key, value in all_metrics.items():
                logger.debug(f"{key}: {value}")

            logger.debug("\n传递给 agent 的指标数据：")
            for key, value in agent_metrics.items():
                logger.debug(f"{key}: {value}")

            return [agent_metrics]

        except Exception as e:
            logger.error(f"Error building indicators: {e}")
            return [{}]

    except Exception as e:
        logger.error(f"Error getting financial indicators: {e}")
        return [{}]


def get_financial_statements(symbol: str) -> Dict[str, Any]:
    """获取财务报表数据"""
    logger.info(f"Getting financial statements for {symbol}...")
    
    fetcher = FinancialDataFetcher()
    
    try:
        # 获取三大财务报表数据
        latest_balance, previous_balance = fetcher._get_financial_report(symbol, "资产负债表")
        latest_income, previous_income = fetcher._get_financial_report(symbol, "利润表")
        latest_cash_flow, previous_cash_flow = fetcher._get_financial_report(symbol, "现金流量表")
        
        # 如果获取失败，使用空Series
        if latest_balance is None:
            latest_balance = previous_balance = pd.Series()
        if latest_income is None:
            latest_income = previous_income = pd.Series()
        if latest_cash_flow is None:
            latest_cash_flow = previous_cash_flow = pd.Series()

        # 构建财务数据
        line_items = []
        try:
            # 处理最新期间数据
            current_item = {
                # 从利润表获取
                "net_income": fetcher._safe_float(latest_income.get("净利润", 0)),
                "operating_revenue": fetcher._safe_float(latest_income.get("营业总收入", 0)),
                "operating_profit": fetcher._safe_float(latest_income.get("营业利润", 0)),

                # 从资产负债表计算营运资金
                "working_capital": (fetcher._safe_float(latest_balance.get("流动资产合计", 0)) - 
                                  fetcher._safe_float(latest_balance.get("流动负债合计", 0))),

                # 从现金流量表获取
                "depreciation_and_amortization": fetcher._safe_float(
                    latest_cash_flow.get("固定资产折旧、油气资产折耗、生产性生物资产折旧", 0)),
                "capital_expenditure": abs(fetcher._safe_float(
                    latest_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))),
                "free_cash_flow": (fetcher._safe_float(latest_cash_flow.get("经营活动产生的现金流量净额", 0)) - 
                                 abs(fetcher._safe_float(latest_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))))
            }
            line_items.append(current_item)
            logger.info("✓ Latest period data processed successfully")

            # 处理上一期间数据
            previous_item = {
                "net_income": fetcher._safe_float(previous_income.get("净利润", 0)),
                "operating_revenue": fetcher._safe_float(previous_income.get("营业总收入", 0)),
                "operating_profit": fetcher._safe_float(previous_income.get("营业利润", 0)),
                "working_capital": (fetcher._safe_float(previous_balance.get("流动资产合计", 0)) - 
                                  fetcher._safe_float(previous_balance.get("流动负债合计", 0))),
                "depreciation_and_amortization": fetcher._safe_float(
                    previous_cash_flow.get("固定资产折旧、油气资产折耗、生产性生物资产折旧", 0)),
                "capital_expenditure": abs(fetcher._safe_float(
                    previous_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))),
                "free_cash_flow": (fetcher._safe_float(previous_cash_flow.get("经营活动产生的现金流量净额", 0)) - 
                                 abs(fetcher._safe_float(previous_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))))
            }
            line_items.append(previous_item)
            logger.info("✓ Previous period data processed successfully")

        except Exception as e:
            logger.error(f"Error processing financial data: {e}")
            default_item = {
                "net_income": 0,
                "operating_revenue": 0,
                "operating_profit": 0,
                "working_capital": 0,
                "depreciation_and_amortization": 0,
                "capital_expenditure": 0,
                "free_cash_flow": 0
            }
            line_items = [default_item, default_item]

        return line_items

    except Exception as e:
        logger.error(f"Error getting financial statements: {e}")
        default_item = {
            "net_income": 0,
            "operating_revenue": 0,
            "operating_profit": 0,
            "working_capital": 0,
            "depreciation_and_amortization": 0,
            "capital_expenditure": 0,
            "free_cash_flow": 0
        }
        return [default_item, default_item]


def get_market_data(symbol: str) -> Dict[str, Any]:
    """获取市场数据"""
    logger.info(f"Getting market data for {symbol}...")

    fetcher = FinancialDataFetcher()

    try:
        # 获取实时行情
        stock_data = fetcher._get_realtime_data(symbol)
        if stock_data is None:
            return {}

        return {
            "market_cap": fetcher._safe_float(stock_data.get("总市值", 0)),
            "volume": fetcher._safe_float(stock_data.get("成交量", 0)),
            # A股没有平均成交量，暂用当日成交量
            "average_volume": fetcher._safe_float(stock_data.get("成交量", 0)),
            "fifty_two_week_high": fetcher._safe_float(stock_data.get("52周最高", 0)),
            "fifty_two_week_low": fetcher._safe_float(stock_data.get("52周最低", 0))
        }

    except Exception as e:
        logger.error(f"Error getting market data: {e}")
        return {}


def get_price_history(symbol: str, start_date: str = None, end_date: str = None, adjust: str = "qfq") -> pd.DataFrame:
    """获取历史价格数据

    Args:
        symbol: 股票代码
        start_date: 开始日期，格式：YYYY-MM-DD，如果为None则默认获取过去一年的数据
        end_date: 结束日期，格式：YYYY-MM-DD，如果为None则使用昨天作为结束日期
        adjust: 复权类型，可选值：
               - "": 不复权
               - "qfq": 前复权（默认）
               - "hfq": 后复权

    Returns:
        包含以下列的DataFrame：
        - date: 日期
        - open: 开盘价
        - high: 最高价
        - low: 最低价
        - close: 收盘价
        - volume: 成交量（手）
        - amount: 成交额（元）
        - amplitude: 振幅（%）
        - pct_change: 涨跌幅（%）
        - change_amount: 涨跌额（元）
        - turnover: 换手率（%）

        技术指标：
        - momentum_1m: 1个月动量
        - momentum_3m: 3个月动量
        - momentum_6m: 6个月动量
        - volume_momentum: 成交量动量
        - historical_volatility: 历史波动率
        - volatility_regime: 波动率区间
        - volatility_z_score: 波动率Z分数
        - atr_ratio: 真实波动幅度比率
        - hurst_exponent: 赫斯特指数
        - skewness: 偏度
        - kurtosis: 峰度
    """
    try:
        # 获取当前日期和昨天的日期
        current_date = datetime.now()
        yesterday = current_date - timedelta(days=1)

        # 如果没有提供日期，默认使用昨天作为结束日期
        if not end_date:
            end_date = yesterday  # 使用昨天作为结束日期
        else:
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
            # 确保end_date不会超过昨天
            if end_date > yesterday:
                end_date = yesterday

        if not start_date:
            start_date = end_date - timedelta(days=365)  # 默认获取一年的数据
        else:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")

        logger.info(f"\nGetting price history for {symbol}...")
        logger.info(f"Start date: {start_date.strftime('%Y-%m-%d')}")
        logger.info(f"End date: {end_date.strftime('%Y-%m-%d')}")

        def get_and_process_data(start_date, end_date):
            """获取并处理数据，包括重命名列等操作"""
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d"),
                adjust=adjust
            )

            if df is None or df.empty:
                return pd.DataFrame()

            # 重命名列以匹配技术分析代理的需求
            df = df.rename(columns={
                "日期": "date",
                "开盘": "open",
                "最高": "high",
                "最低": "low",
                "收盘": "close",
                "成交量": "volume",
                "成交额": "amount",
                "振幅": "amplitude",
                "涨跌幅": "pct_change",
                "涨跌额": "change_amount",
                "换手率": "turnover"
            })

            # 确保日期列为datetime类型
            df["date"] = pd.to_datetime(df["date"])
            return df

        # 获取历史行情数据
        df = get_and_process_data(start_date, end_date)

        if df is None or df.empty:
            logger.warning(f"Warning: No price history data found for {symbol}")
            return pd.DataFrame()

        # 检查数据量是否足够
        min_required_days = 120  # 至少需要120个交易日的数据
        if len(df) < min_required_days:
            logger.warning(f"Warning: Insufficient data ({len(df)} days) for all technical indicators")
            logger.info("Attempting to fetch more data...")

            # 扩大时间范围到2年
            start_date = end_date - timedelta(days=730)
            df = get_and_process_data(start_date, end_date)

            if len(df) < min_required_days:
                logger.warning(f"Warning: Even with extended time range, insufficient data ({len(df)} days)")

        # 计算技术指标
        df = _calculate_technical_indicators(df)

        # 按日期升序排序
        df = df.sort_values("date")

        # 重置索引
        df = df.reset_index(drop=True)

        logger.info(f"Successfully fetched price history data ({len(df)} records)")

        # 检查并报告NaN值
        nan_columns = df.isna().sum()
        if nan_columns.any():
            logger.warning("\nWarning: The following indicators contain NaN values:")
            for col, nan_count in nan_columns[nan_columns > 0].items():
                logger.warning(f"- {col}: {nan_count} records")

        return df

    except Exception as e:
        logger.error(f"Error getting price history: {e}")
        return pd.DataFrame()


def _calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """计算技术指标"""
    try:
        # 计算动量指标
        df["momentum_1m"] = df["close"].pct_change(periods=20)  # 20个交易日约等于1个月
        df["momentum_3m"] = df["close"].pct_change(periods=60)  # 60个交易日约等于3个月
        df["momentum_6m"] = df["close"].pct_change(periods=120)  # 120个交易日约等于6个月

        # 计算成交量动量（相对于20日平均成交量的变化）
        df["volume_ma20"] = df["volume"].rolling(window=20).mean()
        df["volume_momentum"] = df["volume"] / df["volume_ma20"]

        # 计算波动率指标
        # 1. 历史波动率 (20日)
        returns = df["close"].pct_change()
        df["historical_volatility"] = returns.rolling(window=20).std() * np.sqrt(252)  # 年化

        # 2. 波动率区间 (相对于过去120天的波动率的位置)
        volatility_120d = returns.rolling(window=120).std() * np.sqrt(252)
        vol_min = volatility_120d.rolling(window=120).min()
        vol_max = volatility_120d.rolling(window=120).max()
        vol_range = vol_max - vol_min
        df["volatility_regime"] = np.where(
            vol_range > 0,
            (df["historical_volatility"] - vol_min) / vol_range,
            0  # 当范围为0时返回0
        )

        # 3. 波动率Z分数
        vol_mean = df["historical_volatility"].rolling(window=120).mean()
        vol_std = df["historical_volatility"].rolling(window=120).std()
        df["volatility_z_score"] = (df["historical_volatility"] - vol_mean) / vol_std

        # 4. ATR比率
        tr = pd.DataFrame()
        tr["h-l"] = df["high"] - df["low"]
        tr["h-pc"] = abs(df["high"] - df["close"].shift(1))
        tr["l-pc"] = abs(df["low"] - df["close"].shift(1))
        tr["tr"] = tr[["h-l", "h-pc", "l-pc"]].max(axis=1)
        df["atr"] = tr["tr"].rolling(window=14).mean()
        df["atr_ratio"] = df["atr"] / df["close"]

        # 计算统计套利指标
        # 1. 赫斯特指数 (使用过去120天的数据)
        log_returns = np.log(df["close"] / df["close"].shift(1))
        df["hurst_exponent"] = log_returns.rolling(
            window=120,
            min_periods=60  # 要求至少60个数据点
        ).apply(_calculate_hurst)

        # 2. 偏度 (20日)
        df["skewness"] = returns.rolling(window=20).skew()

        # 3. 峰度 (20日)
        df["kurtosis"] = returns.rolling(window=20).kurt()

        return df

    except Exception as e:
        logger.error(f"Error calculating technical indicators: {e}")
        return df


def _calculate_hurst(series):
    """
    计算Hurst指数。

    Args:
        series: 价格序列

    Returns:
        float: Hurst指数，或在计算失败时返回np.nan
    """
    try:
        series = series.dropna()
        if len(series) < 30:  # 降低最小数据点要求
            return np.nan

        # 使用对数收益率
        log_returns = np.log(series / series.shift(1)).dropna()
        if len(log_returns) < 30:  # 降低最小数据点要求
            return np.nan

        # 使用更小的lag范围
        # 减少lag范围到2-10天
        lags = range(2, min(11, len(log_returns) // 4))

        # 计算每个lag的标准差
        tau = []
        for lag in lags:
            # 计算滚动标准差
            std = log_returns.rolling(window=lag).std().dropna()
            if len(std) > 0:
                tau.append(np.mean(std))

        # 基本的数值检查
        if len(tau) < 3:  # 进一步降低最小要求
            return np.nan

        # 使用对数回归
        lags_log = np.log(list(lags))
        tau_log = np.log(tau)

        # 计算回归系数
        reg = np.polyfit(lags_log, tau_log, 1)
        hurst = reg[0] / 2.0

        # 只保留基本的数值检查
        if np.isnan(hurst) or np.isinf(hurst):
            return np.nan

        return hurst

    except Exception as e:
        return np.nan


def prices_to_df(prices):
    """Convert price data to DataFrame with standardized column names"""
    try:
        df = pd.DataFrame(prices)

        # 标准化列名映射
        column_mapping = {
            '收盘': 'close',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'change_percent',
            '涨跌额': 'change_amount',
            '换手率': 'turnover_rate'
        }

        # 重命名列
        for cn, en in column_mapping.items():
            if cn in df.columns:
                df[en] = df[cn]

        # 确保必要的列存在
        required_columns = ['close', 'open', 'high', 'low', 'volume']
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0.0  # 使用0填充缺失的必要列

        return df
    except Exception as e:
        logger.error(f"Error converting price data: {str(e)}")
        # 返回一个包含必要列的空DataFrame
        return pd.DataFrame(columns=['close', 'open', 'high', 'low', 'volume'])


def get_price_data(ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
    """获取股票价格数据

    Args:
        ticker: 股票代码
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD

    Returns:
        包含价格数据的DataFrame
    """
    return get_price_history(ticker, start_date, end_date)

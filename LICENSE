# 双重许可证声明 / Dual Licensing

本项目包含两种不同的许可证，分别适用于原始代码和修改/新增代码。以下为详细说明：

This project is dual-licensed, with different licenses applying to the original code and the modified/new code. Details are as follows:

## 1. 原始代码 - MIT许可证 / Original Code - MIT License
原始代码来自 https://github.com/virattt/ai-hedge-fund，采用MIT许可证。

The original code is sourced from https://github.com/virattt/ai-hedge-fund and is licensed under the MIT License.

MIT License

Copyright (c) 2024 Virat Singh

[MIT许可证原文保持不变]

## 2. 修改和新增代码 - GNU GPL v3 with Non-Commercial Clause
Copyright (c) 2024 24mlight

本项目中由24mlight创建的修改和新增代码部分采用GNU General Public License v3 (GPL v3)，并附加以下条款：
- 禁止将本代码用于任何商业目的，包括但不限于：
  - 在商业产品或服务中使用本代码
  - 销售本代码或其衍生作品
  - 使用本代码提供商业服务或获取商业利益
- 分发的衍生作品必须以GPL v3许可证并遵守非商业条款发布。

The modified and new code created by 24mlight in this project is licensed under the GNU General Public License v3 (GPL v3) with the following additional terms:
- The code may not be used for any commercial purposes, including but not limited to:
  - Use in commercial products or services
  - Selling the code or its derivatives
  - Using the code to provide commercial services or gain commercial benefits
- Derivative works must be distributed under the GPL v3 with the same non-commercial terms.


## 许可证兼容性 / License Compatibility
用户在组合使用本项目的原始代码（MIT许可证）和修改/新增代码（GPL v3 with Non-Commercial Clause）时，必须分别遵守各自的许可证条款。若将两者合并分发，需确保遵守GPL v3的条款及其非商业性限制。

When combining the original code (MIT License) and modified/new code (GPL v3 with Non-Commercial Clause), users must comply with the respective license terms. If distributing both together, compliance with the GPL v3 terms and its non-commercial restrictions is required.

## 注意事项 / Notice
GPL v3附加的非商业条款为自定义限制，用户在使用前应咨询法律意见，以确保符合相关法律法规。

The non-commercial clause added to the GPL v3 is a custom restriction, and users should seek legal advice before use to ensure compliance with applicable laws.

## 联系方式 / Contact
如有许可证相关问题或需要商业使用授权，请联系：<EMAIL>
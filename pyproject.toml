[tool.poetry]
name = "A_Share_investment_Agent"
version = "0.1.0"
description = "An AI-powered hedge fund that uses multiple agents to make trading decisions"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src" }
]
[tool.poetry.dependencies]
python = "^3.9"
langchain = "0.3.0"
langchain-openai = "0.2.11"
langgraph = "0.2.56"
pandas = "^2.1.0"
numpy = "^1.24.0"
python-dotenv = "1.0.0"
matplotlib = "^3.9.2"
yfinance = "^0.2.51"
akshare = "^1.11.22"
requests = "^2.31.0"
beautifulsoup4 = "^4.12.3"
openai = "^1.12.0"
langchain-core = "^0.3.29"
google-generativeai = "^0.3.0"
backoff = "^2.2.1"
google-genai = "^0.6.0"
uvicorn = "^0.34.0"
fastapi = "^0.115.12"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"


[[tool.poetry.source]]
name = "mirrors"
url = "https://repo.huaweicloud.com/repository/pypi/simple"
priority = "supplemental"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"